import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';

import { MPhysicianRoutingModule } from './m-physician-routing.module';
import { MDischargePatientsComponent } from './m-discharge-patients.component';
import { MPendingApprovalEncountersComponent } from './m-pending-approval-encounters.component';
import { MAddPatientComponent } from './m-add-patient.component';
import { MUnbilledEncountersComponent } from './m-unbilled-encounters.component';
import { MStartNewEncounterComponent } from './m-start-new-encounter.component';
import { MApprovePendingEncounterComponent } from './m-approve-pending-encounter.component';
import { MPatientsComponent } from './m-patients.component';
import { MMyPatientsComponent } from './m-my-patients.component';
import { MMyGroupPatientsComponent } from './m-my-group-patients.component';
import { MHospitalCensusComponent } from './m-hospital-census.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MtxNativeDatetimeModule } from '@ng-matero/extensions/core';
import { MtxDatetimepickerModule } from '@ng-matero/extensions/datetimepicker';
import { ChartModule } from 'angular-highcharts';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { PopoverModule } from 'ngx-bootstrap/popover';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { NgxMultipleDatesModule } from 'ngx-multiple-dates';
import { NgxPaginationModule } from 'ngx-pagination';
import { DirectivesModule } from 'src/app/directives/directives.module';
import { SharedModule } from 'src/app/shared.module';
import { CommonTaskModule } from '../common/common.module';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatMenuModule } from '@angular/material/menu';
import { MViewHistoryComponent } from './m-view-history.component';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MViewEncounterHistoryComponent } from './m-view-encounter-history.component';
import { MCPTDataComponent } from './m-cptdata.component';
import { MICDDataComponent } from './m-icddata.component';
import { MSendNoteComponent } from './m-send-note.component';
import { MUploadAttachmentComponent } from './m-upload-attachment.component';
import { MEditPatientComponent } from './m-edit-patient.component';
import { MUndoDischargePatientComponent } from './m-undo-discharge-patient.component';
import { MAssignToOthersOrResidentsComponent } from './m-assign-to-others-or-residents.component';

@NgModule({
  declarations: [
    MMyPatientsComponent,
    MHospitalCensusComponent,
    MMyGroupPatientsComponent,
    MDischargePatientsComponent,
    MPendingApprovalEncountersComponent,
    MAddPatientComponent,
    MUnbilledEncountersComponent,
    MStartNewEncounterComponent,
    MApprovePendingEncounterComponent,
    MPatientsComponent,
    MViewHistoryComponent,
    MViewEncounterHistoryComponent,
    MCPTDataComponent,
    MICDDataComponent,
    MSendNoteComponent,
    MUploadAttachmentComponent,
    MEditPatientComponent,
    MUndoDischargePatientComponent,
    MAssignToOthersOrResidentsComponent
  ],
  imports: [
    CommonModule,
    MatTabsModule,
    MPhysicianRoutingModule,
    ChartModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    NgMultiSelectDropDownModule.forRoot(),
    TooltipModule.forRoot(),
    PopoverModule.forRoot(),
    CommonTaskModule,
    MatInputModule,
    MatNativeDateModule,
    MatDatepickerModule,
    MatIconModule,
    NgxMultipleDatesModule,
    MatFormFieldModule,
    MtxNativeDatetimeModule,
    MtxDatetimepickerModule,
    MatButtonModule,
    MtxDatetimepickerModule,
    SharedModule,
    MatCheckboxModule,
    MatMenuModule,
    MatSelectModule,
    MatSlideToggleModule,
    DirectivesModule
  ]
})
export class MPhysicianModule { }
