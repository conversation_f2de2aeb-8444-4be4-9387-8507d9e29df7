import { Component, OnInit } from '@angular/core';
import { Data, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { PhysicianService } from 'src/app/services/physician/physician.service';
import { DatePipe } from '@angular/common';
import { forkJoin } from 'rxjs';
declare let $: any;

@Component({
  selector: 'app-m-approve-pending-encounter',
  templateUrl: './m-approve-pending-encounter.component.html',
  styleUrls: ['./m-physician.scss']
})
export class MApprovePendingEncounterComponent {

  public listOfUsersAndGroups: Array<any> = [];
    public lisfOfSentNotes: Array<any> = [];
    public selectedUsersAndGroups: Array<any> = [];
    public lisfOfAttachments: Array<any> = [];
    public listOfHistory: Array<any> = [];
    public listOfPatientHistory: Array<any> = [];
    public historyTotalCount: number = 0;
    public backUrl: string = '/physician/my-patients';
    public patient: any = {};
    public listOfPendingEncounters: Array<any> = [];
    public request: any = {};
    public encounterSeenDate: Data = new Date();
    public attachementLastUploadedDate: string = '';
    public attachementCount: number = 0;
    public notesCount: number = 0;
    public residentAccess: string;
    public filterObj: any = {};
  
    constructor(private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService, private readonly appComp: AppComponent,
      private readonly toastr: ToastrService, private readonly physicianService: PhysicianService, private readonly router: Router, public datepipe: DatePipe) { }
  
    ngOnInit(): void {
      this.appComp.loadPageName('Pending Approvals', 'physicianTab');
      this.residentAccess = this.appComp.userAccess.residentAccess;
      this.patient = history.state.patient;
      this.backUrl = history.state.backUrl;
      this.filterObj = history.state.filterObj;
      this.getPendingEncounters(this.patient.facility_Name, this.patient.account_Number);
    }
  
    getPendingEncounters(facility_Name, account_Number) {
      this.commonServ.startLoading();
      let request: any = {};
      request.AccountNumber = this.encrDecr.set(account_Number);
      request.FacilityName = this.encrDecr.set(facility_Name);
      this.physicianService.getPendingEncounters(request).subscribe((p: any) => {
        this.listOfPendingEncounters = p.encountersResponses;
        this.attachementLastUploadedDate = p.attachementLastUploadedDate;
        this.attachementCount = p.attachementCount;
        this.notesCount = p.notesCount;
        this.commonServ.stopLoading();
      }, error => {
        this.commonServ.stopLoading();
        console.error(error.status);
      });
    }
  
    deleteEncounter(item) {
      if (confirm('Do you want to delete this encounter?')) {
        this.commonServ.startLoading();
        let request: any = {};
        request.FacilityName = this.encrDecr.set(this.patient.facility_Name);
        request.strEnounterID = this.encrDecr.set(item.encounteR_ID);
        request.Flag = this.encrDecr.set('Delete');
        this.physicianService.approveorDeleteEncounter(request).subscribe((p: any) => {
          this.router.navigate(['/physician/pending-approval-encounters'], { state: { filterObj: this.filterObj } });
          this.commonServ.stopLoading();
        }, error => {
          this.commonServ.stopLoading();
          console.error(error.status);
        });
      }
    }
  
    approveEncounter(item) {
      if (confirm('Do you want to approve this encounter?')) {
        this.commonServ.startLoading();
        let request: any = {};
        request.FacilityName = this.encrDecr.set(this.patient.facility_Name);
        request.strEnounterID = this.encrDecr.set(item.encounteR_ID);
        request.Flag = this.encrDecr.set('Approve');
        this.physicianService.approveorDeleteEncounter(request).subscribe((p: any) => {
          this.router.navigate(['/physician/pending-approval-encounters'], { state: { filterObj: this.filterObj } });
          this.commonServ.stopLoading();
        }, error => {
          this.commonServ.stopLoading();
          console.error(error.status);
        });
      }
    }
  
    openNotes(item) {
      this.patient = item;
      this.selectedUsersAndGroups = [];
      this.commonServ.startLoading();
      this.request.ACCOUNT_NUMBER = this.encrDecr.set(item.account_Number);
      this.request.FACILITYNAME = this.encrDecr.set(item.facility_Name);
      forkJoin(
        this.commonServ.getUserGroups(item.facility_Name),
        this.commonServ.getNotes(this.request)
      ).subscribe((p: any) => {
        this.listOfUsersAndGroups = p[0];
        this.lisfOfSentNotes = p[1];
        this.notesCount=this.lisfOfSentNotes.length;
        $('#mdlNotes').modal('show');
        this.request = {};
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        this.toastr.error('Something went wrong!!!');
        console.error(error.status);
        this.commonServ.stopLoading();
      });
    }
  
    getAttachments(pObj) {
      this.commonServ.startLoading();
      this.patient = pObj;
      this.request.AccountNumber = this.encrDecr.set(pObj.account_Number);
      this.request.FACILITYNAME = this.encrDecr.set(pObj.facility_Name);
      this.commonServ.getAttachments(this.request).subscribe((p: any) => {
        this.lisfOfAttachments = p;
        this.request = {};
        $('#attachment').modal('show');
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        console.error(error.status);
        this.commonServ.stopLoading();
      });
    }
  
    viewHistory(pObj) {
      this.commonServ.startLoading();
      this.patient = pObj;
      this.request.patientaccountnumber = this.encrDecr.set(pObj.account_Number);
      this.request.FACILITYNAME = this.encrDecr.set(pObj.facility_Name);
      this.request.pagenumber = this.encrDecr.set(20);
      this.physicianService.getPatientHistory(this.request).subscribe((p: any) => {
        this.request = {};
        if (p.length == 0) {
          this.toastr.error('There is No Prior History.');
        }
        else {
          this.listOfPatientHistory = p.listofViewPatientHistory;
          this.historyTotalCount = p.totalcount;
          $('#viewHistory').modal('show');
          $('#viewEnvHistory').hide();
          $('#viewPntHistory').show();
        }
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
    }
  
    updateAttCount(patient: any) {
      this.attachementCount = Number(patient.attachementCount) + 1;
    }
  

}
