<div class="details-header">
    <button class="back-btn" (click)="back()">
        <img src="assets/icons/icon-caret-Left.svg" class="img-icon">
    </button>
    <div class="patient-name ">{{patient.patient_Name}} {{patient.sex}} ({{patient.sex}}Y)</div>
</div>

<div class="w-full bg-white">
    <mat-tab-group (selectedTabChange)="onMatGroupTabClick($event)">
        <mat-tab label="Details">
            <ng-template matTabContent>
                <div class="w-full mobile-p-4">
                    <table class="table table-striped">
                        <tbody>
                            <tr>
                                <td class="text-secondary2">Account</td>
                                <td><span class="font-medium">{{patient.account_Number || '-'}}</span></td>
                            </tr>
                            <tr>
                                <td class="text-secondary2">Facility</td>
                                <td><span class="font-medium">{{patient.facilityName || '-'}}</span></td>
                            </tr>
                            <!-- <tr>
                                <td class="text-secondary2">Type</td>
                                <td><span class="font-medium">{{patient.admission_Type || '-'}}</span></td>
                            </tr>
                            <tr>
                                <td class="text-secondary2">DOB</td>
                                <td><span class="font-medium">{{patient.dbo || '-'}}</span></td>
                            </tr>
                            <tr>
                                <td class="text-secondary2">LOS</td>
                                <td><span class="font-medium">{{patient.arithmetic_Mean_LOS || '-'}}</span></td>
                            </tr> -->
                            <tr>
                                <td class="text-secondary2">Attending</td>
                                <td><span class="font-medium">{{patient.attending_Physician_InApp || '-'}}</span></td>
                            </tr>
                            <!-- <tr>
                                <td class="text-secondary2">Payor</td>
                                <td><span class="font-medium">{{patient.reimbursement_Type || '-'}}</span></td>
                            </tr> -->
                        </tbody>
                    </table>
                    <div class="w-full mt-4">
                        <button class="mark-seen-btn" (click)="viewSubmitEncounterPop()">
                            <span *ngIf="userAccess.residentAccess=='YES';else resEls">Submit for approval</span>
                            <ng-template #resEls>Mark As Seen</ng-template>
                        </button>
                        <div class="details-buttons mt-2">
                            <button class="details-btn" (click)="viewPatientHistory()">
                                <img src="assets/icons/icon-history.svg" class="img-icon">
                            </button>
                            <button class="details-btn" (click)="viewNotes()">
                                <img src="assets/icons/icon-note.svg" class="img-icon">
                            </button>
                            <button class="details-btn" (click)="viewAttachments()">
                                <img src="assets/icons/icon-attachment.svg" class="img-icon">
                            </button>
                        </div>
                    </div>
                </div>
                
            </ng-template>
        </mat-tab>
        <mat-tab label="History">
            <ng-template matTabContent>
                <app-m-view-history [listOfPatientHistory]='listOfPatientHistory' [patient]='patient'
                    [historyTotalCount]="historyTotalCount"></app-m-view-history>
            </ng-template>
        </mat-tab>
        <mat-tab label="Note">
            <ng-template matTabContent>
                <p>Notes</p>
            </ng-template>
        </mat-tab>
        <mat-tab label="Attachments">
            <ng-template matTabContent>
                <p>Attachments</p>
            </ng-template>
        </mat-tab>
    </mat-tab-group>
</div>


<!-- Attachments Popup -->
<div *ngIf="showAttachmentsPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white">
            <span>Attachments</span>
            <button class="close-btn" (click)="closeAttachmentsPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 pt-0 bg-white">
            <div class="attachment-list">
                <div class="attachment-item">
                    <div class="flex justify-between">
                        <div class="flex">
                            <div class="flex items-center">
                                <div
                                    class="flex h-50px w-50px items-center justify-center overflow-hidden rounded-md bg-primary-100">
                                    <div class="flex items-center justify-center text-sm font-medium">
                                        PDF
                                    </div>
                                </div>
                                <div class="ml-2">
                                    <div class="text-md font-medium">
                                        Lorem ipsum.pdf
                                    </div>
                                    <div class="text-secondary text-sm font-medium">
                                        Jhoette Dumlao
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="attachment-actions">
                            <button class="action-btn" (click)="closeAttachmentsPopup()">
                                <img src="assets/icons/icon-Download.svg" class="img-icon">
                            </button>
                            <button class="action-btn" (click)="closeAttachmentsPopup()">
                                <img src="assets/icons/icon-eye.svg" class="img-icon">
                            </button>
                        </div>
                    </div>
                    <div class="flex mt-2">
                        Elit neque amet urna nisl arcu. Vitae enim pellentesque consequat amet neque laoreet
                        posuere. Facilisis sit integer in mi in.
                    </div>
                </div>
                <div class="attachment-item">
                    <div class="flex justify-between">
                        <div class="flex">
                            <div class="flex items-center">
                                <div
                                    class="flex h-50px w-50px items-center justify-center overflow-hidden rounded-md bg-primary-100">
                                    <div class="flex items-center justify-center text-sm font-medium">
                                        PDF
                                    </div>
                                </div>
                                <div class="ml-2">
                                    <div class="text-md font-medium">
                                        Lorem ipsum.pdf
                                    </div>
                                    <div class="text-secondary text-sm font-medium">
                                        Jhoette Dumlao
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="attachment-actions">
                            <button class="action-btn" (click)="closeAttachmentsPopup()">
                                <img src="assets/icons/icon-Download.svg" class="img-icon">
                            </button>
                            <button class="action-btn" (click)="closeAttachmentsPopup()">
                                <img src="assets/icons/icon-eye.svg" class="img-icon">
                            </button>
                        </div>
                    </div>
                    <div class="flex mt-2">
                        Elit neque amet urna nisl arcu. Vitae enim pellentesque consequat amet neque laoreet
                        posuere. Facilisis sit integer in mi in.
                    </div>
                </div>
            </div>
            <!-- Upload Section -->
            <div class="upload-section upload-footer">
                <label class="file-upload-label">
                    <input type="file" style="display:none" />
                    <span class="file-upload-text">
                        <img src="assets/icons/icon-PlusCircle-s.svg" class="upload-icon" />
                        Click here to browse files
                    </span>
                </label>
                <input type="text" class="form-control" placeholder="Comment Optional" />
                <button class="upload-btn">Upload</button>
            </div>
        </div>

    </div>
</div>



<!-- Notes Popup -->
<div *ngIf="showNotesPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideLeft]>
        <div class="actions-header bg-white">
            <span>Notes</span>
            <button class="close-btn" (click)="closeNotesPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-80 min-h-80 pt-0 bg-white">
            <div class="attachment-list">
                <div class="attachment-item">
                    <div class="flex justify-between">
                        <div class="flex">
                            <div class="flex items-center">
                                <div
                                    class="flex h-40px w-40px items-center justify-center overflow-hidden rounded-md bg-primary-100">
                                    <img src="assets/icons/icon-24-white.svg" class="img-icon">
                                </div>
                                <div class="ml-2">
                                    <div class="text-md font-medium">
                                        Aparna Kalluru
                                    </div>
                                    <div class="text-secondary text-sm">
                                        Aug 12, 2025 04:45 PM
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex mt-2">
                        Test comments here
                    </div>
                </div>
                <div class="attachment-item">
                    <div class="flex justify-between">
                        <div class="flex">
                            <div class="flex items-center">
                                <div
                                    class="flex h-40px w-40px items-center justify-center overflow-hidden rounded-md bg-primary-100">
                                    <img src="assets/icons/icon-24-white.svg" class="img-icon">
                                </div>
                                <div class="ml-2">
                                    <div class="text-md font-medium">
                                        Aparna Kalluru
                                    </div>
                                    <div class="text-secondary text-sm">
                                        Aug 12, 2025 04:45 PM
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex mt-2">
                        Test comments here
                    </div>
                </div>
                <div class="attachment-item">
                    <div class="flex justify-between">
                        <div class="flex">
                            <div class="flex items-center">
                                <div
                                    class="flex h-40px w-40px items-center justify-center overflow-hidden rounded-md bg-primary-100">
                                    <img src="assets/icons/icon-24-white.svg" class="img-icon">
                                </div>
                                <div class="ml-2">
                                    <div class="text-md font-medium">
                                        Aparna Kalluru
                                    </div>
                                    <div class="text-secondary text-sm">
                                        Aug 12, 2025 04:45 PM
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex mt-2">
                        Test comments here
                    </div>
                </div>
            </div>
            <!-- Upload Section -->
            <div class="upload-section upload-footer">
                <div class="facility-dropdown mr-0">
                    <mat-label>Users and Groups</mat-label>
                    <mat-select id="subgroup" class="form-control" [value]="1">
                        <mat-option [value]="1">User 1</mat-option>
                        <mat-option [value]="2">User 2</mat-option>
                        <mat-option [value]="3">User 3</mat-option>
                    </mat-select>
                </div>
                <input type="text" class="form-control" placeholder="Comment" />
                <button class="upload-btn">Send</button>
            </div>
        </div>
    </div>
</div>

<!-- Patient History Popup -->
<div *ngIf="showEncountersPopup">
    <div class="modal-backdrop"></div>
    <div class="actions-popup" [@slideUp]>
        <div class="actions-header">
            <span>Past Encounters</span>
            <button class="close-btn" (click)="closePatientHistoryPopup()">
                <img src="assets/icons/icon-close.svg" class="img-icon">
            </button>
        </div>
        <div class="max-h-70">
            <app-m-view-history [listOfPatientHistory]='listOfPatientHistory' [patient]='patient'
                [historyTotalCount]="historyTotalCount"></app-m-view-history>
        </div>
    </div>
</div>