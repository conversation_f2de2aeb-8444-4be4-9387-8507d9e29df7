import { DatePipe } from '@angular/common';
import { Output, Input, EventEmitter, Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';
import { MtxDatetimepickerType, MtxDatetimepickerMode, MtxCalendarView } from '@ng-matero/extensions/datetimepicker';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { CoordinatorService } from 'src/app/services/coordinator/coordinator.service';
declare let $: any;

@Component({
  selector: 'app-m-edit-patient',
  templateUrl: './m-edit-patient.component.html',
  styleUrls: ['./m-physician.scss']
})
export class MEditPatientComponent implements OnInit {
  type: MtxDatetimepickerType = 'datetime';
  mode: MtxDatetimepickerMode = 'portrait';
  startView: MtxCalendarView = 'month';
  multiYearSelector = false;
  touchUi = false;
  twelvehour = false;
  timeInterval = 1;
  timeInput = true;
  @Input() patient: any = {};
  @Input() userType: string = '';
  @Output() eventListOfPatients = new EventEmitter<Array<any>>();
  public patientForm: FormGroup;
  public submitted: boolean = false;
  public request: any = {};
  public listOfPatients: Array<any> = [];
  constructor(private readonly commonServ: CommonService, private readonly fb: FormBuilder, private readonly encrDecr: EncrDecrServiceService, private readonly toastr: ToastrService, private readonly coordinatorServ: CoordinatorService, public datepipe: DatePipe) { }

  ngOnInit(): void {
    this.patientForm = this.fb.group({
      txtFirstName: ['', [Validators.required, Validators.maxLength(125)]],
      txtLastName: ['', [Validators.required, Validators.maxLength(125), UsernameValidatorEdit]],
      txtAccountNo: [''],
      txtMRN: [''],
      txtDOB: [''],
      ddlPatientClass: ['', Validators.required],
      ddlFacilityName: [''],
      txtAdmissionDate: ['', Validators.required],
      txtAssignPatientTo: [''],
      txtDepartment: [''],
      txtRoomNo: ['', Validators.required],
      txtBedNo: ['', Validators.required],
      txtSSN: ['', [Validators.pattern(/^-?(0|[1-9]\d*)?$/), Validators.maxLength(9)]]
    });
  }

  get f() { return this.patientForm.controls; }

  updatePatient(pObj) {
    this.submitted = true;
    if (this.patientForm.invalid) {
      return;
    }
    this.commonServ.startLoading();
    this.request.first_name = this.encrDecr.set(this.patientForm.value.txtFirstName);
    this.request.last_name = this.encrDecr.set(this.patientForm.value.txtLastName);
    this.request.account_number = this.encrDecr.set(pObj.account_number);
    this.request.facility_name = this.encrDecr.set(pObj.facility_name);
    this.request.time_zone_name = this.encrDecr.set(pObj.time_zone_name);
    this.request.residents = this.encrDecr.set(pObj.is_admission_date_editable);
    this.request.patient_class = this.encrDecr.set(this.patientForm.value.ddlPatientClass);
    this.request.room_number = this.encrDecr.set(this.patientForm.value.txtRoomNo);
    this.request.bed_number = this.encrDecr.set(this.patientForm.value.txtBedNo);
    this.request.admit_datetime = this.encrDecr.set(this.datepipe.transform(this.patientForm.value.txtAdmissionDate, 'MM-dd-yyyy hh:mm:ss a'));
    this.request.admission_type = this.encrDecr.set(this.datepipe.transform(this.patientForm.value.txtAdmissionDate, 'MM-dd-yyyy hh:mm:ss a'));
    this.request.user_type = this.encrDecr.set(this.userType);
    this.request.ssn = this.encrDecr.set(this.patientForm.value.txtSSN);
    this.commonServ.updatePatient(this.request).subscribe((p: any) => {
      this.request = {};
      this.submitted = false;
      this.eventListOfPatients.emit(this.listOfPatients);
      this.commonServ.stopLoading();
      this.toastr.success('Patient Updated Successfully!', '', { timeOut: 2500 });
      $('#editPatient').modal('hide');
    },
      error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
  }

}

export function UsernameValidatorEdit(control: FormControl) {
  let lastName = control.root.get('txtLastName');
  if (lastName) {
    let errorObj = { 'cannotContainSpace': false };
    let isEndDateValid = true;
    if (lastName.value) {
      if (lastName.value.indexOf(' ') >= 0) {
        isEndDateValid = false;
      }
    }
    lastName.setErrors((!isEndDateValid) ? errorObj : null);
    if (control.errors) { return { "cannotContainSpace": true } };
  }
  return null;
}
