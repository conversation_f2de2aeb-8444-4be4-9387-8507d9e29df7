import { trigger, style, animate, transition } from '@angular/animations';
import { Component, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AppComponent } from 'src/app/app.component';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { PhysicianService } from 'src/app/services/physician/physician.service';
import { DatePipe } from '@angular/common';
import { forkJoin } from 'rxjs';
import { MtxDatetimepickerType, MtxDatetimepickerMode, MtxCalendarView } from '@ng-matero/extensions/datetimepicker';
import { MatTabChangeEvent } from '@angular/material/tabs';
declare let $: any;

@Component({
  selector: 'app-m-start-new-encounter',
  templateUrl: './m-start-new-encounter.component.html',
  styleUrls: ['./m-physician.scss'],
  animations: [
    trigger('slideUp', [
      transition(':enter', [
        style({ transform: 'translateY(100%)', opacity: 0 }),
        animate('250ms ease-out', style({ transform: 'translateY(0)', opacity: 1 }))
      ]),
      transition(':leave', [
        animate('200ms ease-in', style({ transform: 'translateY(100%)', opacity: 0 }))
      ])
    ]),
    trigger('slideLeft', [
      transition(':enter', [
        style({ transform: 'translateX(100%)', opacity: 0 }),
        animate('250ms ease-out', style({ transform: 'translateX(0)', opacity: 1 }))
      ]),
      transition(':leave', [
        animate('200ms ease-in', style({ transform: 'translateX(100%)', opacity: 0 }))
      ])
    ])
  ]
})
export class MStartNewEncounterComponent {

  showSubmitEncounterPopup = false;
  showCPTCodesPopup = false;
  showICDCodesPopup = false;
  showHistoryPopup = false;
  showAttachmentsPopup = false;
  showNotesPopup = false;

  type: MtxDatetimepickerType = 'datetime';
  mode: MtxDatetimepickerMode = 'portrait';
  startView: MtxCalendarView = 'month';
  multiYearSelector = false;
  touchUi = false;
  twelvehour = false;
  timeInterval = 1;
  timeInput = true;

  public encounterForm: FormGroup;
  public submitted: boolean = false;
  public request: any = {};
  public encounterSeenDate: string = '';
  public backUrl: string = '/m/physician/my-patients';
  public patient: any = {};
  public testData: any = {};
  public lisfOfICDData: Array<any> = [];
  public lisfOfCPTData: Array<any> = [];
  public listOfModifier: Array<any> = [];
  public cptCode: string = '';
  public listOfSelectedModifier: Array<any> = [];
  public listOfProvider: Array<any> = [];
  public selectedProvider: string = '';
  public userAccess: any = {};
  public listOfUsersAndGroups: Array<any> = [];
  public lisfOfSentNotes: Array<any> = [];
  public selectedUsersAndGroups: Array<any> = [];
  public lisfOfAttachments: Array<any> = [];
  public encounterStatus: number = 1;
  public listOfHistory: Array<any> = [];
  public listOfPatientHistory: Array<any> = [];
  public historyTotalCount: number = 0;
  public facilityType: string = '';
  public isMultipleEncounter: boolean = false;
  public datePickerConfig: any = {};
  public multiplueSelectedDates: string = '';
  public listOfEncounterSeenDates: Array<any> = [];
  public encounterSeenDates: string = '';
  public maxDate: Date = new Date();
  public minDate: Date;
  public encounterId: number = 0;
  public filterObj: any = {};
  device = false;
  public maxDateTime: Date = new Date();
  public sub_group_name: string = '';

  constructor(private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService, private readonly appComp: AppComponent,
    private readonly toastr: ToastrService, private readonly physicianService: PhysicianService, private readonly router: Router, public datepipe: DatePipe) {
    const navigation = this.router.getCurrentNavigation();
    if (!navigation) {
      this.router.navigate([this.backUrl], { state: { filterObj: this.filterObj } });
    }
  }

  ngOnInit(): void {
    if (/Android|webOS|iPhone|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) && (window.innerWidth <= 575 || window.innerWidth >= 575)) {
      // some code..
      this.device = true;
    } else {
      this.device = false;
    }

    this.appComp.loadPageName('Encounter Details', 'physicianTab');
    this.userAccess = this.appComp.userAccess;
    this.patient = history.state.patient;
    this.backUrl = history.state.backUrl;
    this.encounterSeenDate = history.state.encounterSeenDate;
    this.facilityType = history.state.facilityType;
    this.filterObj = history.state.filterObj;
    if (history.state.encounterId) {
      this.encounterId = history.state.encounterId;
    }
    this.maxDate.setHours(0, 0, 0, 0);
    this.maxDate.setHours(23, 59, 59, 99);
    let admissionDate = new Date(this.patient.admit_Datetime);
    this.minDate = new Date(admissionDate.setHours(-72))

    if (this.userAccess.residentAccess == 'NO') {
      this.getEncounterTestDetails(this.patient.facility_Name, this.patient.account_Number, this.encounterSeenDate, this.backUrl);
    }
    else {
      this.getEncounterTestDetailsAndProvidersForResident(this.patient.facility_Name, this.patient.account_Number, this.encounterSeenDate, this.backUrl);
    }
  }

  selectMultiSelectDate() {
    if (this.listOfEncounterSeenDates.length == 0 && this.isMultipleEncounter) {
      this.toastr.error('Please select atleast one encounter seen date', '', { timeOut: 1900 });
    }
    else if (!this.testData.encounterSeenDate && !this.isMultipleEncounter) {
      this.toastr.error('Please select encounter seen date', '', { timeOut: 1900 });
    }
    else {
      let datts = '';
      this.listOfEncounterSeenDates.forEach(x => {
        datts = datts + "," + this.datepipe.transform(x, 'MM/dd/yyyy') + ' 01:00 AM';
      });
      this.encounterSeenDates = datts;
      $('#mulitpleDatesModel').modal('hide');
      $('#markAsSeenConfModel').modal('show');
    }
  }

  openMultiSelectPop() {
    if (this.testData.listofTestCPTS == null || this.testData.listofTestCPTS.length == 0) {
      this.toastr.error('Please enter CPT/HCPCS Codes', '', { timeOut: 1900 });
    }
    else if (this.testData.listofTestICDS == null || this.testData.listofTestICDS.length == 0) {
      this.toastr.error('Please enter ICD Codes', '', { timeOut: 1900 });
    }
    else {
      $('#mulitpleDatesModel').modal('show');
    }
  }

  getEncounterTestDetails(facility_Name, account_Number, encounterSeenDate, backUrl) {
    this.commonServ.startLoading();
    this.request.STR_ENCOUNTER_ID = this.encrDecr.set(this.encounterId);
    this.request.FACILITYNAME = this.encrDecr.set(facility_Name);
    this.request.PATIENTACCOUNTNUMBER = this.encrDecr.set(account_Number);
    this.request.EncounterSeenDate = this.encrDecr.set(encounterSeenDate);
    this.request.PageName = this.encrDecr.set(backUrl);
    this.physicianService.getEncounterTestDetails(this.request).subscribe((p: any) => {
      this.testData = p;
      this.testData.encounterSeenDate = new Date(this.testData.encounterSeenDate);
      this.request = {};
      this.commonServ.stopLoading();
    }, error => {
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

  getEncounterTestDetailsAndProvidersForResident(facility_Name, account_Number, encounterSeenDate, backUrl) {
    this.commonServ.startLoading();
    this.request.STR_ENCOUNTER_ID = this.encrDecr.set(this.encounterId);
    this.request.FACILITYNAME = this.encrDecr.set(facility_Name);
    this.request.PATIENTACCOUNTNUMBER = this.encrDecr.set(account_Number);
    this.request.EncounterSeenDate = this.encrDecr.set(encounterSeenDate);
    this.request.PageName = this.encrDecr.set(backUrl);

    forkJoin(
      this.physicianService.getEncounterTestDetails(this.request),
      this.physicianService.getProvidersForResident()
    ).subscribe((p: any) => {
      this.testData = p[0];
      this.testData.encounterSeenDate = new Date(this.testData.encounterSeenDate);
      this.listOfProvider = p[1];
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
    });
  }

  getCPTData() {
    this.commonServ.startLoading();
    $("#favorite-tab").click();
    this.lisfOfCPTData = [];
    this.request.PHYSICIANMAILID = this.encrDecr.set('PHYSICIAN');
    this.commonServ.getCPTData(this.request).subscribe((p: any) => {
      this.lisfOfCPTData = p;
      this.request = {};
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

  getICDData() {
    this.commonServ.startLoading();
    $("#favorite-tab1").click();
    this.lisfOfICDData = [];
    this.request.PHYSICIANMAILID = this.encrDecr.set('PHYSICIAN');
    this.commonServ.getICDData(this.request).subscribe((p: any) => {
      this.lisfOfICDData = p;
      this.request = {};
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

  deleteCPTCode(lisfOfAssignedICDData, cptCode) {
    this.commonServ.startLoading();
    const index: number = lisfOfAssignedICDData.indexOf(cptCode);
    if (index !== -1) {
      lisfOfAssignedICDData.splice(index, 1);
    }
    this.commonServ.stopLoading();
  }


  deleteICDCode(listofTestICDS, icdCode) {
    this.commonServ.startLoading();
    const index: number = listofTestICDS.indexOf(icdCode);
    if (index !== -1) {
      listofTestICDS.splice(index, 1);
    }
    this.commonServ.stopLoading();
  }

  getModifierData(cpt) {
    this.commonServ.startLoading();
    this.listOfModifier = [];
    this.cptCode = cpt;
    let existingModi: Array<any> = [];
    if (cpt.includes('(--,')) {
      let item = cpt.split('(--,')[1].replace(')', "");
      let arry;
      if (item.includes(',')) {
        arry = item.split(',');
        arry.forEach(b => {
          existingModi.push(b);
        });
      }
      else {
        existingModi.push(item);
      }
    }
    this.request.PhysicianEmailId = this.encrDecr.set('PHYSICIAN');
    this.physicianService.getModifiers(this.request).subscribe((p: any) => {
      this.listOfModifier = p;
      this.request = {};
      this.listOfModifier.forEach(x => {
        if (existingModi.includes(x.modifiersname.split('-')[0])) {
          x.isExist = true;
        }
        else {
          x.isExist = false;
        }
      });
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

  back() {
    this.router.navigate([this.backUrl], { state: { filterObj: this.filterObj } });
  }

  starNewEncounter(status) {
    this.submitted = true;
    this.encounterStatus = status;
    if (!this.testData.encounterSeenDate) {
      this.toastr.error('Please select encounter seen date', '', { timeOut: 1900 });
    }
    else if (this.testData.listofTestCPTS == null || this.testData.listofTestCPTS.length == 0) {
      this.toastr.error('Please enter CPT/HCPCS Codes', '', { timeOut: 1900 });
    }
    else if (this.testData.listofTestICDS == null || this.testData.listofTestICDS.length == 0) {
      this.toastr.error('Please enter ICD Codes', '', { timeOut: 1900 });
    }
    else if (this.userAccess.residentAccess == 'YES' && !this.selectedProvider) {
      this.toastr.error('Please select Physician', '', { timeOut: 1900 });
    }
    else {
      $('#markAsSeenConfModel').modal('show');
    }
  }

  confirmStarNewEncounter(status) {
    this.commonServ.startLoading();
    this.request.PATIENTACCOUNTNUMBER = this.encrDecr.set(this.patient.account_Number);
    this.request.FACILITYNAME = this.encrDecr.set(this.patient.facility_Name);
    this.request.FACILITYTIMEZONE = this.encrDecr.set(this.testData.facilityTimeZone);
    this.request.NEW_CPTArray = this.testData.listofTestCPTS;
    this.request.NEW_ICDArray = this.testData.listofTestICDS;
    this.request.ENCOUNTER_STATUS = this.encrDecr.set(status);
    this.request.CLOSE_HOSPITALIZATION_STATUS = this.encrDecr.set("0");
    this.request.MODIFIERS = this.listOfSelectedModifier;
    this.request.ResidentModuleAccess = this.encrDecr.set(this.userAccess.residentAccess);
    this.request.ATTENDING_PROVIDER = this.encrDecr.set(this.selectedProvider);
    this.request.SubGroupName = this.encrDecr.set(this.sub_group_name);
    this.request.IsSubGroup = this.encrDecr.set((this.testData.listofSubGroup?.length > 0 && this.userAccess.residentAccess == 'NO') ? true : false);

    if (this.isMultipleEncounter) {
      this.request.EncounterSeenDates = this.encrDecr.set(this.encounterSeenDates);
      this.physicianService.submitPhysicianMultipleEncounters(this.request).subscribe((p: any) => {
        this.request = {};
        this.encounterSeenDates = '';
        this.listOfSelectedModifier = [];
        this.listOfEncounterSeenDates = [];
        $('#markAsSeenConfModel').modal('hide');
        this.closeSubmitEncounterPop();
        this.commonServ.stopLoading();
        this.toastr.success(p, '', { timeOut: 1900 });
        setTimeout(() => { this.router.navigate([this.backUrl], { state: { filterObj: this.filterObj } }); }, 2000)
      }, error => {
        this.request = {};
        this.listOfSelectedModifier = [];
        this.commonServ.stopLoading();
        console.error(error.status);
      });
    } else {
      this.request.ENCOUNTERSEENDATE = this.encrDecr.set(this.testData.encounterSeenDate ? this.datepipe.transform(this.testData.encounterSeenDate, 'MM/dd/yyyy hh:mm:ss a') : '');
      this.physicianService.startNewEncounter(this.request).subscribe((p: any) => {
        this.request = {};
        this.listOfSelectedModifier = [];
        $('#markAsSeenConfModel').modal('hide');
        this.closeSubmitEncounterPop();
        this.commonServ.stopLoading();
        if (p != 'Unable to save record') {
          this.toastr.success(p, '', { timeOut: 1900 });
        }
        else {
          this.toastr.error(p, '', { timeOut: 1900 });
        }
        setTimeout(() => { this.router.navigate([this.backUrl], { state: { filterObj: this.filterObj } }); }, 2000)
      }, error => {
        this.request = {};
        this.listOfSelectedModifier = [];
        this.commonServ.stopLoading();
        console.error(error.status);
      });
    }
  }

  openNotes(item) {
    this.patient = item;
    this.selectedUsersAndGroups = [];
    this.commonServ.startLoading();
    this.request.ACCOUNT_NUMBER = this.encrDecr.set(item.account_Number);
    this.request.FACILITYNAME = this.encrDecr.set(item.facility_Name);
    forkJoin(
      this.commonServ.getUserGroups(item.facility_Name),
      this.commonServ.getNotes(this.request)
    ).subscribe((p: any) => {
      this.listOfUsersAndGroups = p[0];
      this.lisfOfSentNotes = p[1];
      this.testData.notesCount = this.lisfOfSentNotes.length;
      $('#mdlNotes').modal('show');
      this.request = {};
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.toastr.error('Something went wrong!!!');
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  getAttachments(pObj) {
    this.commonServ.startLoading();
    this.patient = pObj;
    this.request.AccountNumber = this.encrDecr.set(pObj.account_Number);
    this.request.FACILITYNAME = this.encrDecr.set(pObj.facility_Name);
    this.commonServ.getAttachments(this.request).subscribe((p: any) => {
      this.lisfOfAttachments = p;
      this.request = {};
      $('#attachment').modal('show');
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  viewHistory(pObj) {
    this.commonServ.startLoading();
    this.patient = pObj;
    this.request.patientaccountnumber = this.encrDecr.set(pObj.account_Number);
    this.request.FACILITYNAME = this.encrDecr.set(pObj.facility_Name);
    this.request.pagenumber = this.encrDecr.set(20);
    this.physicianService.getPatientHistory(this.request).subscribe((p: any) => {
      this.request = {};
      if (p.length == 0) {
        this.toastr.error('There is No Prior History.');
      }
      else {
        this.listOfPatientHistory = p.listofViewPatientHistory;
        this.historyTotalCount = p.totalcount;
      }
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

  deleteEncounter() {
    if (confirm('Do you want to delete this encounter?')) {
      this.commonServ.startLoading();
      let request: any = {};
      request.FacilityName = this.encrDecr.set(this.patient.facility_Name);
      request.strEnounterID = this.encrDecr.set(this.encounterId);
      request.Flag = this.encrDecr.set('Delete');
      this.physicianService.approveorDeleteEncounter(request).subscribe((p: any) => {
        this.toastr.success('Encounter deleted successfully!', '', { timeOut: 1900 });
        this.router.navigate(['/physician/pending-approval-encounters'], { state: { filterObj: this.filterObj } });
        this.commonServ.stopLoading();
      }, error => {
        this.commonServ.stopLoading();
        console.error(error.status);
      });
    }
  }

  approveAndEdit() {
    if (this.testData.listofTestCPTS == null || this.testData.listofTestCPTS.length == 0) {
      this.toastr.error('Atleast one CPT/HCPCS Code required', '', { timeOut: 1900 });
    }
    else if (this.testData.listofTestICDS == null || this.testData.listofTestICDS.length == 0) {
      this.toastr.error('Atleast one ICD Code required', '', { timeOut: 1900 });
    }
    else {
      this.commonServ.startLoading();
      this.request.FacilityName = this.encrDecr.set(this.patient.facility_Name);
      this.request.strEnounterID = this.encrDecr.set(this.encounterId);
      this.request.NEW_CPTArray = this.testData.listofTestCPTS;
      this.request.NEW_ICDArray = this.testData.listofTestICDS;
      this.request.MODIFIERS = this.listOfSelectedModifier;
      this.physicianService.approveEditEncounterByPhysician(this.request).subscribe((p: any) => {
        this.request = {};
        this.toastr.success(p, '', { timeOut: 1900 });
        this.router.navigate(['/m/physician/pending-approval-encounters'], { state: { filterObj: this.filterObj } });
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
    }
  }

  updateChanges() {
    if (this.testData.listofTestCPTS == null || this.testData.listofTestCPTS.length == 0) {
      this.toastr.error('Atleast one CPT/HCPCS Code required', '', { timeOut: 1900 });
    }
    else if (this.testData.listofTestICDS == null || this.testData.listofTestICDS.length == 0) {
      this.toastr.error('Atleast one  ICD Code required', '', { timeOut: 1900 });
    }
    else {
      this.commonServ.startLoading();
      this.request.FacilityName = this.encrDecr.set(this.patient.facility_Name);
      this.request.strEnounterID = this.encrDecr.set(this.encounterId);
      this.request.NEW_CPTArray = this.testData.listofTestCPTS;
      this.request.NEW_ICDArray = this.testData.listofTestICDS;
      this.request.MODIFIERS = this.listOfSelectedModifier;
      this.physicianService.residentEditCharges(this.request).subscribe((p: any) => {
        this.request = {};
        this.toastr.success(p, '', { timeOut: 1900 });
        this.router.navigate(['/physician/pending-approval-encounters'], { state: { filterObj: this.filterObj } });
        this.commonServ.stopLoading();
      }, error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
    }
  }

  updateAttCount(patient: any) {
    console.log(patient);
    this.testData.attachementCount = Number(this.testData.attachementCount) + 1;
    this.testData.attachementLastUploadedDate = this.datepipe.transform(new Date(), 'MM/dd/yyyy hh:mm:ss');
  }

  onMatGroupTabClick(event: MatTabChangeEvent): void {
    if (event.tab.textLabel == 'History') {
      this.viewHistory(this.patient);
    }
    if (event.tab.textLabel == 'Note') {
      this.openNotes(this.patient);
    }
    if (event.tab.textLabel == 'Attachments') {
      this.getAttachments(this.patient);
    }
  }

  viewCPTCodes() {
    this.showCPTCodesPopup = true;
    this.getCPTData();
  }

  closeCPTCodesPopup() {
    this.showCPTCodesPopup = false;
  }

  viewICDCodes() {
    this.showICDCodesPopup = true;
    this.getICDData();
  }

  closeICDCodesPopup() {
    this.showICDCodesPopup = false;
  }

  viewSubmitEncounterPop() {
    if (this.testData.listofTestCPTS == null || this.testData.listofTestCPTS.length == 0) {
      this.toastr.error('Please enter CPT/HCPCS Codes', '', { timeOut: 1900 });
    }
    else if (this.testData.listofTestICDS == null || this.testData.listofTestICDS.length == 0) {
      this.toastr.error('Please enter ICD Codes', '', { timeOut: 1900 });
    }
    else if (this.userAccess.residentAccess == 'YES' && !this.selectedProvider) {
      this.toastr.error('Please select Physician', '', { timeOut: 1900 });
    }
    else {
      this.showSubmitEncounterPopup = true;
    }
  }

  closeSubmitEncounterPop() {
    this.showSubmitEncounterPopup = false;
  }

  viewPatientHistory() {
    this.showHistoryPopup = true;
    this.viewHistory(this.patient);
  }

  closePatientHistoryPopup() {
    this.showHistoryPopup = false;
  }

  viewAttachments() {
    this.showAttachmentsPopup = true;
    this.getAttachments(this.patient);
  }

  closeAttachmentsPopup() {
    this.showAttachmentsPopup = false;
  }

  viewNotes() {
    this.showNotesPopup = true;
    this.openNotes(this.patient);
  }

  closeNotesPopup() {
    this.showNotesPopup = false;
  }

}
