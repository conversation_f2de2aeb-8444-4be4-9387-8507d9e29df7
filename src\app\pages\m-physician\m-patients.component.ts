import { DatePipe } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { forkJoin } from 'rxjs';
import { CommonService } from 'src/app/services/common/common.service';
import { EncrDecrServiceService } from 'src/app/services/common/encr-decr-service.service';
import { PhysicianService } from 'src/app/services/physician/physician.service';
import { AppComponent } from 'src/app/app.component';
declare let $: any;
import { trigger, style, animate, transition } from '@angular/animations';

@Component({
  selector: 'app-m-patients',
  templateUrl: './m-patients.component.html',
  styleUrls: ['./m-physician.scss'],
    animations: [
      trigger('slideUp', [
        transition(':enter', [
          style({ transform: 'translateY(100%)', opacity: 0 }),
          animate('250ms ease-out', style({ transform: 'translateY(0)', opacity: 1 }))
        ]),
        transition(':leave', [
          animate('200ms ease-in', style({ transform: 'translateY(100%)', opacity: 0 }))
        ])
      ])
    ]
})
export class MPatientsComponent   implements OnInit {
  showHistoryPopup = false;
  showAttachmentsPopup = false;
  showNotesPopup = false;
  showActionsPopup = false;
  showEditPatientPopup = false;
  showAssignPopup = false;
  showUndoDischargePopup = false;

  public submitted: boolean = false;
  public request: any = {};
  @Input() FilterForm: FormGroup;
  @Input() p: number = 1;
  @Input() public listOfProvider: Array<any> = [];
  @Input() listOfPatients: Array<any> = [];
  @Input() listOfFacilities: Array<any> = [];
  public listOfPhysicians: Array<any> = [];
  public encounterSeenDate: Date = new Date();
  public timeout: any = null;
  @Input() searchByName: string = '';
  public listOfUsersAndGroups: Array<any> = [];
  public lisfOfSentNotes: Array<any> = [];
  public selectedUsersAndGroups: Array<any> = [];
  public patient: any = {};
  public listOfPatientHistory: Array<any> = [];
  public historyTotalCount: number = 0;
  public lisfOfAttachments: Array<any> = [];
  @Input() totalCount: number = 0;
  public listOfGroups: Array<any> = [];
  public userName: string;
  public residentAccess: string;
  @Input() device:boolean;
  public filterObj: any = {};

  orderByPatient = 'desc';
  orderByRoom = 'desc';
  orderByAccount = 'desc';
  orderByAdm = 'desc';
  orderByFacility = 'desc';
  orderByAge = 'desc';
  orderByPtType = 'desc';
  orderByPayer = 'desc';
  orderByLOS = 'desc';
  attendingPhysician = 'desc';
  orderByDischarge_Date = 'desc';

  @Input() orderBy: string = 'desc';
  @Input() sortColumnBy: string = 'room_Number';

  hideSideNav = false;
  toggleType = 'close';
  sortOrders = ['desc', 'asc'];
  @Input() userAccess: any = {};
  @Input() isSelectAllChecked: boolean = false;


  sortOptions = [
    {
      id: 'account_Number', name: 'Account Number', sortOrders: ['desc', 'asc']
    },
    {
      id: 'patient_Name', name: 'Patient Name', sortOrders: ['desc', 'asc']
    },
    {
      id: 'facility_Name', name: 'Facility', sortOrders: ['desc', 'asc']
    },
    {
      id: 'age', name: 'Age', sortOrders: ['desc', 'asc']
    },
    {
      id: 'room_Number', name: 'Room', sortOrders: ['desc', 'asc']
    },
    {
      id: 'admission_Type', name: 'Patient Type', sortOrders: ['desc', 'asc']
    },
    {
      id: 'arithmetic_Mean_LOS', name: 'LOS', sortOrders: ['desc', 'asc']
    },
    {
      id: 'reimbursement_Type', name: 'Payer Class', sortOrders: ['desc', 'asc']
    },
    {
      id: 'attending_Physician_InApp', name: 'Attending Provider', sortOrders: ['desc', 'asc']
    }
  ];

  public mDdlPhysicianSettings: IDropdownSettings = {};
  @Input() pageName:string='';
  @Output() eventListOfPatients = new EventEmitter<any>();
  @Output() eventRemovePatient=new EventEmitter<any>();
  @Output() eventChkHaveanyToApprove = new EventEmitter<any>();
  @Output() eventChkAllForApprove=new EventEmitter<any>();
  @Output() enventUpdateSortObj=new EventEmitter<any>();
  constructor(private readonly commonServ: CommonService, private readonly encrDecr: EncrDecrServiceService
    ,private readonly toastr: ToastrService, private readonly physicianService: PhysicianService, public datepipe: DatePipe, private readonly appComp: AppComponent) {
      this.residentAccess = this.appComp.userAccess.residentAccess;
    }

  ngOnInit() {
    /** mobile */
    if (this.device && this.sortColumnBy) {
      const sortOrder = this.sortOptions.filter(x => x.id == this.sortColumnBy);
      this.sortOrders = sortOrder.length ? sortOrder[0].sortOrders : ['desc', 'asc'];
    }
    else if (this.device) {
      this.sortOrders = ['desc', 'asc'];
    }
  }

  getPatients(pageNo:any){
    this.p=pageNo;
    this.eventListOfPatients.emit(pageNo);
  }

  RemovePatient(item) {
    this.eventRemovePatient.emit(item);
  }

  assignToMyListSave(item) {
    if (confirm('Do you want to assign this patient to your list?')) {
      this.submitted = true;
      this.commonServ.startLoading();
      this.request.Account_Number = this.encrDecr.set(item.account_Number);
      this.request.FacilityName = this.encrDecr.set(item.facility_Name);
      this.physicianService.assignToMyList(this.request).subscribe((p: any) => {
        this.request = {};
        this.toastr.success("Patient assigned to your list successfully", '', { timeOut: 2500 });
        this.eventListOfPatients.emit(this.p);
      }, error => { console.error(error.status); }
      );
    }
  }

  assignToOthersPopup(item) {
    this.submitted = true;
    this.commonServ.startLoading();
    this.patient = item;
    this.getPhysiciansByFacility(item.facility_Name);
    $('#mdlPhysician').modal('show');
    this.commonServ.stopLoading();
  }

  getPhysiciansByFacility(facillity) {
    this.commonServ.startLoading();
    this.commonServ.getPhysiciansByUserType(facillity, 'PHYSICIAN').subscribe((p: any) => {
      this.commonServ.stopLoading();
      this.listOfPhysicians = p;
    }, error => {
      this.commonServ.stopLoading();
    });
  }

  getResidents(facillity) {
    this.commonServ.startLoading();
    this.physicianService.GetResidents(facillity).subscribe((p: any) => {
      this.listOfProvider = p;
      this.commonServ.stopLoading();
    }, error => {
      this.commonServ.stopLoading();
      console.error(error.status);
    });
  }

  assignToResidentPopup(item) {
    this.submitted = true;
    this.commonServ.startLoading();
    this.patient = item;
    this.getResidents(item.facility_Name);
    $('#mdlResiPhysician').modal('show');
    this.commonServ.stopLoading();
  }

  openNotes(item) {
    this.patient = item;
    this.selectedUsersAndGroups = [];
    this.commonServ.startLoading();
    this.request.ACCOUNT_NUMBER = this.encrDecr.set(item.account_Number);
    this.request.FACILITYNAME = this.encrDecr.set(item.facility_Name);
    forkJoin(
      this.commonServ.getUserGroups(item.facility_Name),
      this.commonServ.getNotes(this.request)
    ).subscribe((p: any) => {
      this.listOfUsersAndGroups = p[0];
      this.lisfOfSentNotes = p[1];
      item.notesCount=this.lisfOfSentNotes.length;
      $('#mdlNotes').modal('show');
      this.request = {};
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.toastr.error('Something went wrong!!!');
      console.error(error.status);
      this.commonServ.stopLoading();
    });
  }

  viewHistory(pObj) {
    this.commonServ.startLoading();
    this.request.patientaccountnumber = this.encrDecr.set(pObj.account_Number);
    this.request.facilityname = this.encrDecr.set(pObj.facility_Name);
    this.request.pagenumber = this.encrDecr.set(20);
    this.physicianService.getPatientHistory(this.request).subscribe((p: any) => {
      this.request = {};
      this.listOfPatientHistory = p.listofViewPatientHistory;
      this.historyTotalCount = p.totalcount;
      this.patient = pObj;
      $('#viewHistory').modal('show');
      $('#viewEnvHistory').hide();
      $('#viewPntHistory').show();
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      this.commonServ.stopLoading();
      console.error(error.status);
    });
    $("body").click();
  }

  getAttachments(pObj) {
    this.commonServ.startLoading();
    this.patient = pObj;
    this.request.AccountNumber = this.encrDecr.set(pObj.account_Number);
    this.request.FacilityName = this.encrDecr.set(pObj.facility_Name);
    this.commonServ.getAttachments(this.request).subscribe((p: any) => {
      this.lisfOfAttachments = p;
      this.request = {};
      $('#attachment').modal('show');
      this.commonServ.stopLoading();
    }, error => {
      this.request = {};
      console.error(error.status);
      this.commonServ.stopLoading();
    });
    $("body").click();
  }

  UpdatePosttoBiller(pObj) {
    this.submitted = true;
    this.commonServ.startLoading();
    this.request.Accountnumber = this.encrDecr.set(pObj.account_Number);
    this.request.PostToBiller = this.encrDecr.set('1');
    this.request.CloseHospilization = this.encrDecr.set('');
    this.request.FacilityName = this.encrDecr.set(pObj.facility_Name);
    this.physicianService.CloseAndPostBillerStatus(this.request).subscribe((p: any) => {
      this.request = {};
      this.eventListOfPatients.emit(this.p);
      this.commonServ.stopLoading();
      this.toastr.success("Post to Biller status changed successfully", '', { timeOut: 2500 });
    }, error => { console.error(error.status); }
    );
    $("body").click();
  }

  CloseHospitalizationStatus(pObj) {
    this.submitted = true;
    this.commonServ.startLoading();
    this.request.Accountnumber = this.encrDecr.set(pObj.account_Number);
    this.request.PostToBiller = this.encrDecr.set('');
    this.request.CloseHospilization = this.encrDecr.set('1');
    this.request.FacilityName = this.encrDecr.set(pObj.facility_Name);
    this.physicianService.CloseAndPostBillerStatus(this.request).subscribe((p: any) => {
      this.request = {};
      this.eventListOfPatients.emit(this.p);
      this.commonServ.stopLoading();
      this.toastr.success("Closed Hospitalization successfully", '', { timeOut: 2500 });
    }, error => { console.error(error.status); }
    );
    $("body").click();
  }

  openEditPatient(item) {
    this.commonServ.startLoading();
    this.request.account_number = this.encrDecr.set(item.account_Number);
    this.request.facility_name = this.encrDecr.set(item.facility_Name);
    this.commonServ.getPatientInfo(this.request).subscribe((p: any) => {
      this.request = {};
      this.patient = p;
      this.patient.admit_datetime = new Date(this.patient.admit_datetime);
      $('#editPatient').modal('show');
      this.commonServ.stopLoading();
    },
      error => {
        this.request = {};
        this.commonServ.stopLoading();
        console.error(error.status);
      });
    $("body").click();
  }

  updateOrderByPatient(){
    this.orderByPatient = this.orderByPatient == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByRoom(){
    this.orderByRoom = this.orderByRoom == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByAccount(){
    this.orderByAccount = this.orderByAccount == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByFacility(){
    this.orderByFacility = this.orderByFacility == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByAge(){
    this.orderByAge = this.orderByAge == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByPtType(){
    this.orderByPtType = this.orderByPtType == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByPayer(){
    this.orderByPayer = this.orderByPayer == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByAdm(){
    this.orderByAdm = this.orderByAdm == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByDischargeDate(){
    this.orderByDischarge_Date = this.orderByDischarge_Date == 'asc' ? 'desc' : 'asc';
  }
  updateOrderByLOS(){
    this.orderByLOS = this.orderByLOS == 'asc' ? 'desc' : 'asc';
  }
  updateAttendingPhysician(){
    this.attendingPhysician = this.attendingPhysician == 'asc' ? 'desc' : 'asc';
  }

  sortColumn(columnBy) {

    this.sortColumnBy = columnBy;
    switch (columnBy) {
      case ('patient_Name'): {
        this.updateOrderByPatient();
        this.orderBy = this.orderByPatient;
        this.sortColumnBy = 'patient_Name';
        break;
      }
      case ('room_Number'): {
        this.updateOrderByRoom();
        this.orderBy = this.orderByRoom;
        this.sortColumnBy = 'room_Number';
        break;
      }
      case ('account_Number'): {
        this.updateOrderByAccount();
        this.orderBy = this.orderByAccount;
        this.sortColumnBy = 'account_Number';
        break;
      }
      case ('facility_Name'): {
        this.updateOrderByFacility();
        this.orderBy = this.orderByFacility;
        this.sortColumnBy = 'facility_Name';
        break;
      }
      case ('age'): {
        this.updateOrderByAge();
        this.orderBy = this.orderByAge;
        this.sortColumnBy = 'age';
        break;
      }
      case ('admission_Type'): {
        this.updateOrderByPtType();
        this.orderBy = this.orderByPtType;
        this.sortColumnBy = 'admission_Type';
        break;
      }
      case ('reimbursement_Type'): {
        this.updateOrderByPayer();
        this.orderBy = this.orderByPayer;
        this.sortColumnBy = 'reimbursement_Type';
        break;
      }
      case ('admission_Date'): {
        this.updateOrderByAdm();
        this.orderBy = this.orderByAdm;
        this.sortColumnBy = 'admission_Date';
        break;
      }
      case ('arithmetic_Mean_LOS'): {
        this.updateOrderByLOS();
        this.orderBy = this.orderByLOS;
        this.sortColumnBy = 'arithmetic_Mean_LOS';
        break;
      }
      case ('attending_Physician_InApp'): {
        this.updateAttendingPhysician();
        this.orderBy = this.attendingPhysician;
        this.sortColumnBy = 'attending_Physician_InApp';
        break;
      }
      case ('discharge_Date'): {
        this.updateOrderByDischargeDate();
        this.orderBy = this.orderByDischarge_Date;
        this.sortColumnBy = 'discharge_Date';
        break;
      }
      default: {
        break;
      }
    }
    this.enventUpdateSortObj.emit({ sortColumnBy: this.sortColumnBy, orderBy: this.orderBy });
    this.eventListOfPatients.emit(this.p);

  }

  HidePatient(item) {
    if (confirm('Do you want to hide this patient?')) {
      this.submitted = true;
      this.commonServ.startLoading();
      this.request.Account_Number = this.encrDecr.set(item.account_Number);
      this.request.FacilityName = this.encrDecr.set(item.facility_Name);
      this.request.IsHide = this.encrDecr.set('1');
      this.physicianService.HideOrUnHidePatient(this.request).subscribe((p: any) => {
        this.request = {};
        this.eventListOfPatients.emit(this.p);
        this.commonServ.stopLoading();
        this.toastr.success("Hide Patient successfully", '', { timeOut: 2500 });
      }, error => { console.error(error.status); }
      )
    }
  }

  UnHidePatient(item) {
    if (confirm('Do you want to unhide this patient?')) {
      this.submitted = true;
      this.commonServ.startLoading();
      this.request.Account_Number = this.encrDecr.set(item.account_Number);
      this.request.FacilityName = this.encrDecr.set(item.facility_Name);
      this.request.IsHide = this.encrDecr.set('0');
      this.physicianService.HideOrUnHidePatient(this.request).subscribe((p: any) => {
        this.request = {};
        this.eventListOfPatients.emit(this.p);
        this.commonServ.stopLoading();
        this.toastr.success("Un-Hide Patient successfully", '', { timeOut: 2500 });
      }, error => { console.error(error.status); }
      )
    }
  }

  openUnDischargePatientPop(item) {
    this.commonServ.startLoading();
    this.patient = item;
    $('#UnDischargePopup').modal('show');
    this.commonServ.stopLoading();
  }

  chkAllForApprove(event: any) {
    this.eventChkAllForApprove.emit(event);
  }

  chkHaveanyToApprove() {
    this.eventChkHaveanyToApprove.emit();
  }

  approveEncounterMyMobile(item: any) {
    if (confirm('Do you want to approve this encounters?')) {
      this.commonServ.startLoading();
      let request: any = {};
      request.FacilityName = this.encrDecr.set(item.facility_Name);
      request.strEnounterID = this.encrDecr.set(item.encounteR_ID);
      request.Flag = this.encrDecr.set('Approve');
      this.physicianService.approvePendingEncounters(request).subscribe((p: any) => {
        this.commonServ.stopLoading();
        this.toastr.success("Selected Encounters are Approved Successfully", '', { timeOut: 2500 });
        this.eventListOfPatients.emit(this.p);
      }, error => {
        this.commonServ.stopLoading();
        console.error(error.status);
      });
    }
  }

  updateAttCount(patient:any) {
    patient.attachementCount=Number(patient.attachementCount)+1;
  }

  openActionsPopup() {
    this.showActionsPopup = true;
  }

  closeActionsPopup() {
    this.showActionsPopup = false;
  }

  viewPatientHistory(item:any) {
    this.showHistoryPopup = true;
    this.viewHistory(item);
  }

  closePatientHistoryPopup() {
    this.showHistoryPopup = false;
  }

  viewAttachments(item:any) {
    this.showAttachmentsPopup = true;
    this.getAttachments(item);
  }

  closeAttachmentsPopup() {
    this.showAttachmentsPopup = false;
  }

  viewNotes(item:any) {
    this.showNotesPopup = true;
    this.openNotes(item);
  }

  closeNotesPopup() {
    this.showNotesPopup = false;
  }

  viewEditPatient(item:any) {
    this.showEditPatientPopup = true;
    this.openEditPatient(item);
  }

  closeEditPatientPopup() {
    this.showEditPatientPopup = false;
  }

  viewAssign(item:any) {
    this.showAssignPopup = true;
    this.openNotes(item);
  }

  closeAssignPopup() {
    this.showAssignPopup = false;
  }

  viewUndoDischarge(item:any) {
    this.showUndoDischargePopup = true;
    this.openNotes(item);
  }

  closeUndoDischargePopup() {
    this.showUndoDischargePopup = false;
  }

}
